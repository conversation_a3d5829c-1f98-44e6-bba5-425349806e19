import { ref, onMounted, computed, h, onUnmounted, watch } from 'vue';
import { useDialog } from 'naive-ui';
import { RefreshOutline, AddOutline, TrashOutline, PencilOutline, HelpCircleOutline, ExpandOutline, ContractOutline } from '@vicons/ionicons5';
import { getRoles, getMenus, saveRole, updateRole, deleteRole, getRoleDetail } from '@/api/roles';
import { getDepartments } from '@/api/users';
import messages from '@/utils/messages';
import { NButton, NSpace, NIcon, NModal } from 'naive-ui';
import DepartmentSelector from '@/components/users/DepartmentSelector.vue';

// 导出composable函数
export function useRolesPage() {
  // 响应式数据
  const roles = ref([]);
  const menuTree = ref([]);
  const dialogVisible = ref(false);
  const isEdit = ref(false);
  const formRef = ref(null);
  const menuTreeRef = ref(null);
  const selectedRoles = ref([]);

  const form = ref({
    roleName: '',
    roleCode: '',
    menus: [],
    dataScope: '',
    dataScopeDepts: []
  });

  // 表单验证规则
  const rules = {
    roleName: [
      {
        required: true,
        message: '请输入角色名称（2-20个汉字）',
        trigger: ['blur', 'input'],
        validator: (_rule, value) => {
          if (!value) {
            return new Error('角色名称不能为空');
          } else if (value.length < 2 || value.length > 20) {
            return new Error('角色名称长度应在2-20个字符之间');
          }
          return true;
        }
      }
    ],
    roleCode: [
      {
        required: true,
        message: '请输入角色代码（2-20个大写字母或下划线）',
        trigger: ['blur', 'input'],
        validator: (_rule, value) => {
          console.log('验证角色代码:', value, '类型:', typeof value);
          if (!value || value.trim() === '') {
            return new Error('角色代码不能为空');
          } else if (!/^[a-zA-Z_]{2,20}$/.test(value)) {
            return new Error('角色代码应为2-20个大写字母或下划线');
          }
          return true;
        }
      }
    ],
    menus: [
      {
        type: 'array',
        required: true,
        message: '请至少选择一个菜单权限',
        trigger: 'change',
        validator: (_rule, value) => {
          if (!value || value.length === 0) {
            return new Error('请至少选择一个菜单权限');
          }
          return true;
        }
      }
    ],
    dataScope: [
      {
        trigger: 'change',
        validator: () => {
          return true;
        }
      }
    ]
  };

  const dialog = useDialog();

  // 表格列配置
  const columns = [
    { type: 'selection', align: 'center' },
    { title: '角色ID', key: 'id', align: 'center' },
    { title: '角色代码', key: 'roleCode', align: 'center' },
    { title: '角色名称', key: 'roleName', align: 'center' },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      align: 'center',
      render: (row) => {
        return h(NSpace, { justify: 'center', size: 'small' }, () => [
          h(NIcon, {
            component: PencilOutline,
            size: 20,
            color: '#2080f0',
            style: {
              cursor: 'pointer'
            },
            onClick: () => handleEdit(row)
          }),
          h(NIcon, {
            component: TrashOutline,
            size: 20,
            color: '#d03050',
            style: {
              cursor: 'pointer'
            },
            onClick: () => handleDelete(row)
          })
        ]);
      }
    }
  ];

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const response = await getRoles();
      roles.value = response.data.map(role => ({
        ...role,
        menus: role.menus || []
      }));
    } catch (error) {
      messages.error('获角色列失败');
    }
  };

  // 获取菜单树
  const fetchMenuTree = async () => {
    try {
      console.log('开始获取菜单数据');
      const response = await getMenus();
      console.log('成功获取菜单数据:', response);
      menuTree.value = buildMenuTree(response.data);
    } catch (error) {
      console.error('获取菜单数据失败:', error);
      messages.error(`获取菜单列表失败: ${error.message || '未知错误'}`);
    }
  };

  // 构建菜单树
  const buildMenuTree = (menus) => {
    const menuMap = {};
    menus.forEach(menu => menuMap[menu.id] = { ...menu, subMenus: [] });

    const rootMenus = [];
    menus.forEach(menu => {
      if (menu.parentId === 1) {
        rootMenus.push(menuMap[menu.id]);
      } else if (menuMap[menu.parentId]) {
        // 确保父菜单存在再添加子菜单
        menuMap[menu.parentId].subMenus.push(menuMap[menu.id]);
      }
    });

    // 移除空的 subMenus 数组
    const removeEmptySubMenus = (menu) => {
      if (!menu || !menu.subMenus) {
        return; // 如果菜单项不存在或没有 subMenus 属性，直接返回
      }

      if (menu.subMenus.length === 0) {
        delete menu.subMenus;
      } else {
        menu.subMenus.forEach(removeEmptySubMenus);
      }
    };
    rootMenus.forEach(removeEmptySubMenus);

    return rootMenus;
  };

  // 刷新角色列表
  const refreshRoles = () => {
    fetchRoles();
  };

  // 显示新增对话框
  const showAddDialog = () => {
    isEdit.value = false;
    form.value = { roleName: '', roleCode: '', menus: [], dataScope: '', dataScopeDepts: [] };
    dialogVisible.value = true;
  };

  // 处理编辑
  const handleEdit = async (row) => {
    isEdit.value = true;
    dialogVisible.value = true;

    try {
      // 获取角色详情
      const roleResponse = await getRoleDetail(row.id);
      const roleDetail = roleResponse.data;

      // 调试信息：打印角色详情数据
      console.log('获取到的角色详情数据:', roleDetail);
      console.log('角色代码:', roleDetail.roleCode, '类型:', typeof roleDetail.roleCode);

      // 获取所有部门数据，用于匹配部门名称
      const deptResponse = await getDepartments();
      const allDepartments = deptResponse.data || [];
      const deptMap = {};
      allDepartments.forEach(dept => {
        deptMap[dept.id] = dept;
      });

      // 处理数据权限部门数据
      let dataScopeDepts = [];
      if (roleDetail.dataScope) {
        const deptIds = roleDetail.dataScope.split(',');
        // 将字符串ID转换为DepartmentSelector需要的对象格式，并添加名称
        dataScopeDepts = deptIds
          .filter(id => deptMap[id]) // 过滤掉不存在的部门ID
          .map(id => ({
            id: id,
            name: deptMap[id]?.name || `部门${id}` // 使用部门名称，如果找不到则使用默认名称
          }));
      }

      // 确保roleCode不为null或undefined
      const roleCode = roleDetail.roleCode || '';
      console.log('处理后的角色代码:', roleCode);

      form.value = {
        id: roleDetail.id,
        roleName: roleDetail.roleName,
        roleCode: roleCode, // 使用处理后的roleCode
        menus: roleDetail.menus || [],
        dataScope: roleDetail.dataScope || '',
        dataScopeDepts: dataScopeDepts
      };

      // 打印设置后的表单数据
      console.log('设置后的表单数据:', form.value);
    } catch (error) {
      console.error('获取角色详情或部门数据失败:', error);
      messages.error('获取角色详情失败');
    }
  };

  // 处理删除
  const handleDelete = (row) => {
    dialog.warning({
      title: '警告',
      content: '确定要删除这个角色吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await deleteRole(row.id);
          messages.success('删除成功');
          fetchRoles();
        } catch (error) {
          messages.error('删除失败');
        }
      }
    });
  };

  // 处理批量删除
  const batchDelete = () => {
    if (selectedRoles.value.length === 0) {
      messages.warning('请选择要删除的角色');
      return;
    }
    dialog.warning({
      title: '警告',
      content: `确定要删除这 ${selectedRoles.value.length} 个角色吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await Promise.all(selectedRoles.value.map(role => deleteRole(role.id)));
          messages.success('批量删除成功');
          fetchRoles();
        } catch (error) {
          messages.error('批量删除失败');
        }
      }
    });
  };

  // 处理选择变化
  const handleSelectionChange = (selection) => {
    selectedRoles.value = selection;
  };

  // 处理菜单选择
  const handleMenuCheck = (checkedKeys) => {
    form.value.menus = checkedKeys;
    // 使用 Promise 风格的验证，并处理可能的验证错误
    if (formRef.value) {
      formRef.value.validate()
        .then(() => {
          // 验证成功
        })
        .catch((errors) => {
          // 验证失败，处理错误
          console.error('表单验证失败:', errors);
        });
    }
  };

  // 监听dataScopeDepts变化，更新dataScope字段
  watch(() => form.value.dataScopeDepts, (newValue) => {
    if (Array.isArray(newValue) && newValue.length > 0) {
      form.value.dataScope = newValue.map(dept => dept.id).join(',');
    } else {
      form.value.dataScope = '';
    }
  }, { deep: true });

  // 保存或更新角色
  const handleSave = async () => {
    if (!formRef.value) return;

    // 在验证前打印表单数据
    console.log('保存前的表单数据:', form.value);

    // 确保 roleCode 字段不为空
    if (!form.value.roleCode || form.value.roleCode.trim() === '') {
      messages.error('角色代码不能为空');
      return;
    }

    await formRef.value.validate();
    const roleData = {
      ...form.value,
      menus: form.value.menus,
      roleCode: form.value.roleCode.trim() // 确保去除空格
    };

    console.log('准备提交的角色数据:', roleData);
    try {
      if (isEdit.value) {
        await updateRole(roleData);
        messages.success('角色更新成功');
      } else {
        await saveRole(roleData);
        messages.success('角色创建成功');
      }
      dialogVisible.value = false;
      fetchRoles();
    } catch (errors) {
      console.error('表单验证失败:', errors);
      messages.error('请检查表单填写是否正确');
    }
  };

  // 弹窗相关状态
  const isFullscreen = ref(false);
  const showMaximizeButton = ref(false);

  // 计算对话框宽度
  const dialogWidth = computed(() => {
    if (isFullscreen.value) {
      return '100%';
    }
    return window.innerWidth < 1920 ? '75%' : '35%';
  });

  // 监听窗口大小变化
  const handleResize = () => {
    showMaximizeButton.value = window.innerWidth < 1920;
    if (window.innerWidth < 1920 && dialogVisible.value) {
      isFullscreen.value = true;
    }
  };

  // 切换全屏状态
  const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
  };

  // 监听对话框可见性变化
  watch(dialogVisible, (newValue) => {
    if (newValue && window.innerWidth < 1920) {
      isFullscreen.value = true;
    } else {
      isFullscreen.value = false;
    }
  });

  // 生命周期钩子
  onMounted(() => {
    fetchRoles();
    fetchMenuTree();
    window.addEventListener('resize', handleResize);
    handleResize(); // 初始化时调用一次
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });

  // 返回所有需要在模板中使用的变量和方法
  return {
    // 响应式数据
    roles,
    menuTree,
    dialogVisible,
    isEdit,
    formRef,
    menuTreeRef,
    selectedRoles,
    form,
    rules,
    columns,
    isFullscreen,
    showMaximizeButton,
    dialogWidth,

    // 图标组件
    RefreshOutline,
    AddOutline,
    TrashOutline,
    PencilOutline,
    HelpCircleOutline,
    ExpandOutline,
    ContractOutline,

    // 组件
    DepartmentSelector,

    // 方法
    refreshRoles,
    showAddDialog,
    handleEdit,
    handleDelete,
    batchDelete,
    handleSelectionChange,
    handleMenuCheck,
    handleSave,
    toggleFullscreen
  };
}
