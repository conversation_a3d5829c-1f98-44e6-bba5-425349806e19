<template>
  <div class="role-management">
    <!-- 工具栏 -->
    <n-space class="toolbar">
      <n-button type="primary" @click="refreshRoles" round>
        <template #icon>
          <n-icon>
            <RefreshOutline />
          </n-icon>
        </template>
        刷新数据
      </n-button>
      <n-button type="info" @click="showAddDialog" round>
        <!-- 修改按钮颜色为蓝色 -->
        <template #icon>
          <n-icon>
            <AddOutline />
          </n-icon>
        </template>
        新增角色
      </n-button>
      <n-button
        type="error"
        @click="batchDelete"
        :disabled="!selectedRoles.length"
        round
      >
        <template #icon>
          <n-icon>
            <TrashOutline />
          </n-icon>
        </template>
        批量删除
      </n-button>
    </n-space>

    <!-- 角色列表 -->
    <n-data-table
      :columns="columns"
      :data="roles"
      :row-key="(row) => row.id"
      @update:checked-row-keys="handleSelectionChange"
    />

    <!-- 编辑角色对话框 -->
    <n-modal
      v-model:show="dialogVisible"
      :title="isEdit ? '编辑角色' : '新增角色'"
      :style="{ width: dialogWidth }"
      preset="card"
      :mask-closable="false"
      :auto-focus="false"
      :transformOrigin="'center'"
      :class="{ 'fullscreen-modal': isFullscreen }"
    >
      <template #header-extra>
        <n-button
          v-if="showMaximizeButton"
          quaternary
          circle
          @click="toggleFullscreen"
        >
          <template #icon>
            <n-icon>
              <component :is="isFullscreen ? ContractOutline : ExpandOutline" />
            </n-icon>
          </template>
        </n-button>
      </template>

      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        :style="{
          maxWidth: '640px',
        }"
      >
        <n-form-item label="角色名称" path="roleName">
          <n-input v-model:value="form.roleName" placeholder="请输入角色名称" />
        </n-form-item>
        <n-form-item label="角色代码" path="roleCode">
          <n-input
            v-model:value="form.roleCode"
            placeholder="请输入角色代码"
            @blur="form.roleCode = form.roleCode?.trim() || ''"
            @input="
              (val) => {
                console.log('角色代码输入值:', val);
              }
            "
          />
        </n-form-item>
        <n-grid :cols="2" :x-gap="24">
          <n-grid-item>
            <n-form-item label="菜单权限" path="menus">
              <n-tree
                ref="menuTreeRef"
                :data="menuTree"
                checkable
                cascade
                :key-field="'id'"
                :label-field="'menuLabel'"
                :children-field="'subMenus'"
                :checked-keys="form.menus"
                @update:checked-keys="handleMenuCheck"
                :default-expand-all="true"
                :selectable="false"
                :show-irrelevant-nodes="false"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="数据权限" path="dataScope">
              <n-tooltip placement="top">
                <template #trigger>
                  <n-icon>
                    <HelpCircleOutline />
                  </n-icon>
                </template>
                不选择数据权限时仅能查询自己名下的数据
              </n-tooltip>
              <department-selector
                v-model="form.dataScopeDepts"
                mode="multiple"
                label="选择数据权限范围"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="dialogVisible = false">取消</n-button>
          <n-button type="primary" @click="handleSave">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import {
  // 响应式数据
  roles,
  menuTree,
  dialogVisible,
  isEdit,
  formRef,
  menuTreeRef,
  selectedRoles,
  form,
  rules,
  columns,
  isFullscreen,
  showMaximizeButton,
  dialogWidth,

  // 图标组件
  RefreshOutline,
  AddOutline,
  TrashOutline,
  HelpCircleOutline,
  ExpandOutline,
  ContractOutline,

  // 方法
  refreshRoles,
  showAddDialog,
  batchDelete,
  handleSelectionChange,
  handleMenuCheck,
  handleSave,
  toggleFullscreen,
} from "./RolesPage.js";

// 单独导入组件，因为需要在模板中使用kebab-case
import DepartmentSelector from "@/components/users/DepartmentSelector.vue";
</script>

<style lang="scss" scoped>
@use "./RolesPage.scss";
</style>